/**
 * CategoryProductCarousel Component
 *
 * A server-side rendered carousel component that displays products from a given category slug.
 * It recursively fetches products from all child categories and randomizes them for better variety.
 *
 * Features:
 * - Server-side rendering with caching
 * - Recursive category traversal (fetches from all subcategories)
 * - Product randomization/mixing from different child categories
 * - Responsive design with customizable items per view
 * - Optional navigation controls
 * - Uses existing ProductItem component for consistent styling
 *
 * Usage:
 * ```tsx
 * <CategoryProductCarousel
 *   categorySlug="food"
 *   title="Food Items"
 *   maxProducts={15}
 *   showNavigation={true}
 *   itemsPerView={{
 *     mobile: 2,
 *     tablet: 3,
 *     desktop: 4,
 *     large: 6
 *   }}
 * />
 * ```
 */

import { getPrisma } from "@udoy/utils/db-utils";
import ProductItem from "./ProductItem";
import {
  unstable_cacheTag as cacheTag,
  unstable_cacheLife as cacheLife,
} from "next/cache";
import { CacheKey } from "@udoy/utils/cache-key";
import { getLocale } from "next-intl/server";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@udoy/components/ui/carousel";
import {
  Category,
  Product,
  ProductAvailability,
  ProductImage,
  QuantityUnit,
} from "@prisma/client";

type ProductWithRelations = Product & {
  images: ProductImage[];
  unit: QuantityUnit;
  category: { slug: string } | null;
  availability: ProductAvailability[];
};

interface CategoryProductCarouselProps {
  categorySlug: string;
  title?: string;
  maxProducts?: number;
  className?: string;
  showNavigation?: boolean;
  autoplay?: boolean;
  itemsPerView?: {
    mobile: number;
    tablet: number;
    desktop: number;
    large: number;
  };
}

async function getCategoryWithAllProducts(categorySlug: string): Promise<ProductWithRelations[]> {
  "use cache";
  cacheTag(CacheKey.CategoryProducts(categorySlug));
  cacheTag(CacheKey.AllCategoryProducts());
  cacheLife("hours");

  const prisma = getPrisma();

  try {
    // First, get the category and check if it exists
    const category = await prisma.category.findUnique({
      where: { slug: categorySlug, hide: false },
      select: { id: true, isBase: true },
    });

    if (!category) {
      console.warn(`Category with slug "${categorySlug}" not found or is hidden`);
      return [];
    }

  // Function to recursively get all category IDs including subcategories
  async function getAllCategoryIds(categoryId: string): Promise<string[]> {
    const categoryIds = [categoryId];
    
    // Get all subcategories recursively
    const subcategories = await prisma.category.findMany({
      where: { 
        parentId: categoryId,
        hide: false,
      },
      select: { id: true },
    });

    // Recursively get subcategories of subcategories
    for (const subcategory of subcategories) {
      const subIds = await getAllCategoryIds(subcategory.id);
      categoryIds.push(...subIds);
    }

    return categoryIds;
  }

  // Get all category IDs (including nested subcategories)
  const allCategoryIds = await getAllCategoryIds(category.id);

  // Fetch all products from these categories
  const products = await prisma.product.findMany({
    where: {
      categoryId: {
        in: allCategoryIds,
      },
      hide: false,
      category: {
        hide: false,
      },
    },
    include: {
      images: true,
      unit: true,
      category: { select: { slug: true } },
      availability: {
        orderBy: {
          priority: 'desc'
        }
      },
    },
    orderBy: [
      { featured: "desc" },
      { position: "asc" },
    ],
  });

    return products;
  } catch (error) {
    console.error(`Error fetching products for category "${categorySlug}":`, error);
    return [];
  }
}

function shuffleArray<T>(array: T[]): T[] {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
}

async function CategoryProductCarousel({
  categorySlug,
  title,
  maxProducts = 12,
  className = "",
  showNavigation = true,
  autoplay = false,
  itemsPerView = {
    mobile: 2,
    tablet: 3,
    desktop: 4,
    large: 6,
  },
}: CategoryProductCarouselProps) {
  const products = await getCategoryWithAllProducts(categorySlug);
  const locale = await getLocale();

  if (products.length === 0) {
    return null;
  }

  // Shuffle products to mix different child categories
  const shuffledProducts = shuffleArray(products);
  
  // Limit the number of products
  const limitedProducts = shuffledProducts.slice(0, maxProducts);

  // Generate responsive classes based on itemsPerView
  const responsiveClasses = `pl-2 md:pl-4 basis-1/${itemsPerView.mobile} sm:basis-1/${itemsPerView.tablet} md:basis-1/${itemsPerView.desktop} xl:basis-1/${itemsPerView.large}`;

  return (
    <div className={`mt-8 w-full ${className}`}>
      {title && (
        <h3 className="text-2xl font-bold text-center mb-6">{title}</h3>
      )}

      <div className="px-6">
        <Carousel
          className="w-full"
          opts={{
            align: "start",
            loop: autoplay,
          }}
        >
          <CarouselContent className="-ml-2 md:-ml-4">
            {limitedProducts.map((product) => (
              <CarouselItem
                key={product.id}
                className={responsiveClasses}
              >
                <ProductItem product={product} locale={locale} />
              </CarouselItem>
            ))}
          </CarouselContent>
          {showNavigation && (
            <>
              <CarouselPrevious className="left-0" />
              <CarouselNext className="right-0" />
            </>
          )}
        </Carousel>
      </div>
    </div>
  );
}

export default CategoryProductCarousel;
