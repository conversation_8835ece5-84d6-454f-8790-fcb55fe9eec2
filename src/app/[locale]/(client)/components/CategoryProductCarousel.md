# CategoryProductCarousel Component

A server-side rendered carousel component that displays products from a given category slug using shadcn carousel. It recursively fetches products from all child categories and randomizes them for better variety.

## Features

- ✅ **Server-side rendering** with caching for optimal performance
- ✅ **Recursive category traversal** - fetches products from all subcategories
- ✅ **Product randomization** - mixes products from different child categories
- ✅ **Responsive design** with customizable items per view
- ✅ **Optional navigation controls** (previous/next buttons)
- ✅ **Uses existing ProductItem component** for consistent styling
- ✅ **Comprehensive error handling** with logging
- ✅ **Cache optimization** with proper invalidation tags

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `categorySlug` | `string` | **Required** | The slug of the category to fetch products from |
| `title` | `string?` | `undefined` | Optional title to display above the carousel |
| `maxProducts` | `number` | `12` | Maximum number of products to display |
| `className` | `string` | `""` | Additional CSS classes for styling |
| `showNavigation` | `boolean` | `true` | Whether to show previous/next navigation buttons |
| `autoplay` | `boolean` | `false` | Whether to enable autoplay (loop) |
| `itemsPerView` | `object` | See below | Responsive breakpoints for items per view |

### Default `itemsPerView` Configuration

```typescript
{
  mobile: 2,    // 2 items on mobile
  tablet: 3,    // 3 items on tablet
  desktop: 4,   // 4 items on desktop
  large: 6      // 6 items on large screens
}
```

## Basic Usage

```tsx
import CategoryProductCarousel from "../components/CategoryProductCarousel";

// Simple usage
<CategoryProductCarousel 
  categorySlug="food" 
  title="Food Items" 
/>

// With custom configuration
<CategoryProductCarousel 
  categorySlug="fruits-vegetables" 
  title="Fresh Fruits & Vegetables" 
  maxProducts={20}
  showNavigation={false}
  className="bg-green-50 rounded-lg p-4"
  itemsPerView={{
    mobile: 1,
    tablet: 2,
    desktop: 3,
    large: 4
  }}
/>
```

## How It Works

1. **Category Resolution**: Finds the category by slug and validates it exists and is not hidden
2. **Recursive Traversal**: Recursively fetches all subcategory IDs (including nested subcategories)
3. **Product Fetching**: Queries all products from the category and its subcategories
4. **Randomization**: Shuffles products to mix items from different child categories
5. **Rendering**: Displays products in a responsive carousel using shadcn components

## Caching Strategy

The component uses Next.js caching with the following tags:
- `CategoryProducts(categorySlug)` - Specific to the category
- `AllCategoryProducts()` - Global category products cache

Cache lifetime: **1 hour** (`cacheLife("hours")`)

### Cache Invalidation

When updating products or categories in admin actions, invalidate the cache:

```typescript
import { revalidateTag } from "next/cache";
import { CacheKey } from "@udoy/utils/cache-key";

// When updating products in a specific category
revalidateTag(CacheKey.CategoryProducts("category-slug"));

// When updating categories or products globally
revalidateTag(CacheKey.AllCategoryProducts());
```

## Error Handling

The component gracefully handles:
- ❌ Non-existent categories (returns null, no render)
- ❌ Hidden categories (returns null, no render)
- ❌ Database errors (logs error, returns null)
- ❌ Empty product lists (returns null, no render)

## Performance Considerations

- **Server-side rendering** reduces client-side JavaScript
- **Caching** prevents repeated database queries
- **Recursive queries** are optimized with proper indexing
- **Product limiting** prevents excessive DOM elements
- **Responsive classes** ensure optimal display on all devices

## Integration with Home Page

Add multiple carousels to your home page for better product discovery:

```tsx
// In your home page component
<div className="space-y-8">
  <CategoryProductCarousel 
    categorySlug="food" 
    title="Featured Food Items" 
    maxProducts={15}
  />
  
  <CategoryProductCarousel 
    categorySlug="fruits-vegetables" 
    title="Fresh Produce" 
    maxProducts={12}
  />
  
  <CategoryProductCarousel 
    categorySlug="fresh-vegetable" 
    title="Farm Fresh Vegetables" 
    maxProducts={10}
    showNavigation={false}
  />
</div>
```

## Troubleshooting

### Component doesn't render
- Check if the category slug exists in the database
- Verify the category is not hidden (`hide: false`)
- Check if there are products in the category or its subcategories

### Products not updating
- Ensure cache invalidation is called after product/category updates
- Check if products are hidden (`hide: false`)
- Verify category relationships are correct

### Performance issues
- Reduce `maxProducts` if rendering too many items
- Check database indexes on category and product tables
- Monitor cache hit rates

## Dependencies

- `@udoy/utils/db-utils` - Database connection
- `@udoy/utils/cache-key` - Cache key management
- `@udoy/components/ui/carousel` - Shadcn carousel components
- `next-intl/server` - Internationalization
- `next/cache` - Next.js caching utilities
