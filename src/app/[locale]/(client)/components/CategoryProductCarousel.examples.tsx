/**
 * CategoryProductCarousel Usage Examples
 * 
 * This file contains various examples of how to use the CategoryProductCarousel component
 * in different scenarios for your home page or other pages.
 */

import CategoryProductCarousel from "./CategoryProductCarousel";

// Example 1: Basic usage with default settings
export function BasicFoodCarousel() {
  return (
    <CategoryProductCarousel 
      categorySlug="food" 
      title="Food Items" 
    />
  );
}

// Example 2: Customized carousel with more products and no navigation
export function LargeFruitsVegetablesCarousel() {
  return (
    <CategoryProductCarousel 
      categorySlug="fruits-vegetables" 
      title="Fresh Fruits & Vegetables" 
      maxProducts={20}
      showNavigation={false}
      className="bg-green-50 rounded-lg p-4"
    />
  );
}

// Example 3: Mobile-optimized carousel
export function MobileOptimizedCarousel() {
  return (
    <CategoryProductCarousel 
      categorySlug="food" 
      title="Popular Food Items" 
      maxProducts={10}
      itemsPerView={{
        mobile: 1,
        tablet: 2,
        desktop: 3,
        large: 4
      }}
      autoplay={true}
    />
  );
}

// Example 4: Multiple carousels for home page
export function HomePageCarousels() {
  return (
    <div className="space-y-8">
      {/* Featured Food Items */}
      <CategoryProductCarousel 
        categorySlug="food" 
        title="Featured Food Items" 
        maxProducts={15}
        className="bg-gradient-to-r from-orange-50 to-red-50 rounded-xl p-6"
      />
      
      {/* Fresh Produce */}
      <CategoryProductCarousel 
        categorySlug="fruits-vegetables" 
        title="Fresh Fruits & Vegetables" 
        maxProducts={12}
        className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-6"
      />
      
      {/* Compact carousel without title */}
      <CategoryProductCarousel 
        categorySlug="fresh-vegetable" 
        maxProducts={8}
        showNavigation={false}
        itemsPerView={{
          mobile: 2,
          tablet: 4,
          desktop: 6,
          large: 8
        }}
      />
    </div>
  );
}

// Example 5: Category-specific themed carousel
export function ThemedVegetableCarousel() {
  return (
    <div className="bg-green-900 text-white rounded-2xl p-8">
      <CategoryProductCarousel 
        categorySlug="fresh-vegetable" 
        title="🥬 Farm Fresh Vegetables" 
        maxProducts={16}
        className="text-white"
        itemsPerView={{
          mobile: 2,
          tablet: 3,
          desktop: 4,
          large: 5
        }}
      />
    </div>
  );
}

// Example 6: Responsive grid-like carousel
export function ResponsiveGridCarousel() {
  return (
    <CategoryProductCarousel 
      categorySlug="food" 
      title="All Food Categories" 
      maxProducts={24}
      itemsPerView={{
        mobile: 2,
        tablet: 3,
        desktop: 6,
        large: 8
      }}
      showNavigation={true}
      className="container mx-auto"
    />
  );
}

/**
 * Usage in your pages:
 * 
 * 1. Import the component:
 *    import CategoryProductCarousel from "../components/CategoryProductCarousel";
 * 
 * 2. Use it in your page component:
 *    <CategoryProductCarousel categorySlug="your-category-slug" title="Your Title" />
 * 
 * 3. For home page, you can use multiple carousels:
 *    <HomePageCarousels />
 * 
 * 4. Remember to handle cache invalidation in your admin actions:
 *    - When products are updated: revalidateTag(CacheKey.CategoryProducts(categorySlug))
 *    - When categories are updated: revalidateTag(CacheKey.AllCategoryProducts())
 */
