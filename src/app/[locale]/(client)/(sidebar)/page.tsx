import PopularProducts from "./components/PopularProducts";
import { setRequestLocale } from "next-intl/server";
import PopularCategories from "./components/PopularCategories";
import { routing } from "@udoy/i18n/routing";
import CategoryProductCarousel from "../components/CategoryProductCarousel";

async function Home({
  params,
}: {
  params: Promise<{ locale: typeof routing.defaultLocale }>;
}) {
  const { locale } = await params;

  setRequestLocale(locale);

  return (
    <div
      className="flex-1 overflow-y-scroll pb-6"
      style={{ height: "calc(100vh - 64px)" }}
    >
      <div className="w-[calc(100vw-40px)] xl:w-[calc(100vw-256px)] mx-auto">
        <CategoryProductCarousel
          categorySlug="fresh-market"
          maxProducts={15}
          autoplay
          delay={2000}
        />
      </div>
      <div className="w-[calc(100vw-40px)] xl:w-[calc(100vw-256px)] mx-auto">
        <CategoryProductCarousel
          categorySlug="food"
          maxProducts={15}
          autoplay
          delay={1000}
        />
      </div>
      <PopularCategories />
      <PopularProducts />

      {/* Example usage of CategoryProductCarousel */}

      {/* <CategoryProductCarousel
        categorySlug="fruits-vegetables"
        title="Fresh Fruits & Vegetables"
        maxProducts={12}
        showNavigation={false}
      /> */}
    </div>
  );
}

export default Home;
