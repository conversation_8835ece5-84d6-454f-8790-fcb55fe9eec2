import PopularProducts from "./components/PopularProducts";
import { setRequestLocale } from "next-intl/server";
import PopularCategories from "./components/PopularCategories";
import { routing } from "@udoy/i18n/routing";
import CategoryProductCarousel from "../components/CategoryProductCarousel";

async function Home({ params }: { params: Promise<{ locale: typeof routing.defaultLocale }> }) {
  const { locale } = await params;

  setRequestLocale(locale);

  return (
    <div
      className="flex-1 overflow-y-scroll pb-6"
      style={{ height: "calc(100vh - 64px)" }}
    >
      <PopularCategories />
      <PopularProducts />

      {/* Example usage of CategoryProductCarousel */}
      <CategoryProductCarousel
        categorySlug="food"
        title="Food Items"
        maxProducts={15}
      />

      <CategoryProductCarousel
        categorySlug="fruits-vegetables"
        title="Fresh Fruits & Vegetables"
        maxProducts={12}
      />
    </div>
  );
}

export default Home;
